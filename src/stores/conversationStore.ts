import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import type { ConversationState } from '../services/impl/ISpeechRecognitionService';

export interface ConversationStoreState {
  // Core conversation state
  conversationState: ConversationState;
  isActive: boolean;
  isListening: boolean;
  error: string | null;
  
  // Smart microphone control
  smartMicrophoneEnabled: boolean;
  
  // Audio state tracking
  isAudioPlaying: boolean;
  
  // Actions
  setConversationState: (state: ConversationState) => void;
  setIsActive: (active: boolean) => void;
  setIsListening: (listening: boolean) => void;
  setError: (error: string | null) => void;
  setSmartMicrophoneEnabled: (enabled: boolean) => void;
  setIsAudioPlaying: (playing: boolean) => void;
  
  // Computed getters
  shouldBeListening: () => boolean;
  
  // Reset function
  reset: () => void;
}

const initialState = {
  conversationState: 'idle' as ConversationState,
  isActive: false,
  isListening: false,
  error: null,
  smartMicrophoneEnabled: true,
  isAudioPlaying: false,
};

export const useConversationStore = create<ConversationStoreState>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    
    setConversationState: (state: ConversationState) => {
      const currentState = get().conversationState;
      if (currentState !== state) {
        console.log(`🔄 [Store] Estado de conversación: ${currentState} → ${state}`);
        set({ conversationState: state });
      }
    },
    
    setIsActive: (active: boolean) => {
      const currentActive = get().isActive;
      if (currentActive !== active) {
        console.log(`🔄 [Store] Conversación activa: ${currentActive} → ${active}`);
        set({ isActive: active });
      }
    },
    
    setIsListening: (listening: boolean) => {
      const currentListening = get().isListening;
      if (currentListening !== listening) {
        console.log(`🔄 [Store] Escuchando: ${currentListening} → ${listening}`);
        set({ isListening: listening });
      }
    },
    
    setError: (error: string | null) => {
      const currentError = get().error;
      if (currentError !== error) {
        console.log(`🔄 [Store] Error: ${currentError} → ${error}`);
        set({ error });
      }
    },
    
    setSmartMicrophoneEnabled: (enabled: boolean) => {
      const currentEnabled = get().smartMicrophoneEnabled;
      if (currentEnabled !== enabled) {
        console.log(`🔄 [Store] Smart microphone: ${currentEnabled} → ${enabled}`);
        set({ smartMicrophoneEnabled: enabled });
      }
    },
    
    setIsAudioPlaying: (playing: boolean) => {
      const currentPlaying = get().isAudioPlaying;
      if (currentPlaying !== playing) {
        console.log(`🔄 [Store] Audio playing: ${currentPlaying} → ${playing}`);
        set({ isAudioPlaying: playing });
      }
    },
    
    shouldBeListening: () => {
      const state = get();
      
      console.log('🔍 [Store] Evaluando shouldBeListening:', {
        smartMicrophoneEnabled: state.smartMicrophoneEnabled,
        conversationState: state.conversationState,
        isAudioPlaying: state.isAudioPlaying,
        isListening: state.isListening,
        isActive: state.isActive,
      });
      
      if (!state.smartMicrophoneEnabled) {
        console.log('❌ [Store] Smart microphone disabled');
        return false;
      }
      
      if (!state.isActive) {
        console.log('❌ [Store] Conversation not active');
        return false;
      }
      
      if (state.conversationState !== 'idle') {
        console.log('❌ [Store] Conversation state is not idle:', state.conversationState);
        return false;
      }
      
      if (state.isAudioPlaying) {
        console.log('❌ [Store] Audio is currently playing');
        return false;
      }
      
      if (state.error) {
        console.log('❌ [Store] There is an error:', state.error);
        return false;
      }
      
      console.log('✅ [Store] Should be listening');
      return true;
    },
    
    reset: () => {
      console.log('🔄 [Store] Resetting conversation store');
      set(initialState);
    },
  }))
);

// Selector hooks for specific state slices
export const useConversationState = () => useConversationStore(state => state.conversationState);
export const useIsActive = () => useConversationStore(state => state.isActive);
export const useIsListening = () => useConversationStore(state => state.isListening);
export const useConversationError = () => useConversationStore(state => state.error);
export const useSmartMicrophoneEnabled = () => useConversationStore(state => state.smartMicrophoneEnabled);
export const useIsAudioPlaying = () => useConversationStore(state => state.isAudioPlaying);

// Action hooks
export const useConversationActions = () => useConversationStore(state => ({
  setConversationState: state.setConversationState,
  setIsActive: state.setIsActive,
  setIsListening: state.setIsListening,
  setError: state.setError,
  setSmartMicrophoneEnabled: state.setSmartMicrophoneEnabled,
  setIsAudioPlaying: state.setIsAudioPlaying,
  shouldBeListening: state.shouldBeListening,
  reset: state.reset,
}));

// Subscribe to state changes for debugging
if (typeof window !== 'undefined') {
  useConversationStore.subscribe(
    (state) => state.conversationState,
    (conversationState, previousConversationState) => {
      if (conversationState !== previousConversationState) {
        console.log(`🔄 [Store Subscription] Conversation state changed: ${previousConversationState} → ${conversationState}`);
      }
    }
  );
  
  useConversationStore.subscribe(
    (state) => state.isActive,
    (isActive, previousIsActive) => {
      if (isActive !== previousIsActive) {
        console.log(`🔄 [Store Subscription] Active state changed: ${previousIsActive} → ${isActive}`);
      }
    }
  );
}
